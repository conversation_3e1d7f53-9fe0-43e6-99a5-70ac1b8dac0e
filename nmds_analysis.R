# NMDS Ordination Analysis for Bacteria, Fungi, and Metazoa
# Author: Generated for microbiome analysis
# Date: 2025-08-22

# Load required libraries
library(vegan)
library(readxl)
library(ggplot2)
library(dplyr)
library(gridExtra)
library(RColorBrewer)

# Set working directory (adjust if needed)
setwd("c:/Users/<USER>/Desktop/beta")

# Function to load and prepare data for each dataset
load_dataset <- function(otu_file, metadata_file, taxonomy_file, dataset_name) {
  cat("Loading", dataset_name, "dataset...\n")
  
  # Load data
  otu_table <- read_excel(otu_file)
  metadata <- read_excel(metadata_file)
  taxonomy <- read_excel(taxonomy_file)
  
  # Display structure for debugging
  cat("OTU table dimensions:", dim(otu_table), "\n")
  cat("Metadata dimensions:", dim(metadata), "\n")
  cat("Metadata columns:", colnames(metadata), "\n")
  
  # Check if required columns exist
  if (!"Locality" %in% colnames(metadata)) {
    stop(paste("'Locality' column not found in", dataset_name, "metadata"))
  }
  if (!"habitat_type" %in% colnames(metadata)) {
    stop(paste("'habitat_type' column not found in", dataset_name, "metadata"))
  }
  
  return(list(otu = otu_table, metadata = metadata, taxonomy = taxonomy))
}

# Function to prepare OTU matrix (assuming first column is sample names)
prepare_otu_matrix <- function(otu_table) {
  # Assume first column contains sample names
  sample_names <- otu_table[,1]
  otu_matrix <- as.matrix(otu_table[,-1])
  rownames(otu_matrix) <- as.character(sample_names[[1]])
  
  # Remove columns with all zeros
  otu_matrix <- otu_matrix[, colSums(otu_matrix) > 0]
  
  return(otu_matrix)
}

# Function to perform NMDS analysis and create plots
perform_nmds_analysis <- function(dataset_list, dataset_name) {
  cat("\n=== Analyzing", dataset_name, "dataset ===\n")
  
  # Prepare OTU matrix
  otu_matrix <- prepare_otu_matrix(dataset_list$otu)
  metadata <- dataset_list$metadata
  
  # Ensure metadata matches OTU samples
  # Assuming first column of metadata contains sample names
  sample_col <- colnames(metadata)[1]
  metadata_matched <- metadata[metadata[[sample_col]] %in% rownames(otu_matrix), ]
  otu_matrix_matched <- otu_matrix[rownames(otu_matrix) %in% metadata_matched[[sample_col]], ]
  
  # Reorder to match
  metadata_matched <- metadata_matched[match(rownames(otu_matrix_matched), metadata_matched[[sample_col]]), ]
  
  cat("Final matrix dimensions:", dim(otu_matrix_matched), "\n")
  cat("Metadata rows:", nrow(metadata_matched), "\n")
  
  # Calculate Bray-Curtis distance
  dist_matrix <- vegdist(otu_matrix_matched, method = "bray")
  
  # Perform NMDS
  set.seed(123)  # For reproducibility
  nmds_result <- metaMDS(dist_matrix, k = 2, trymax = 100)
  
  cat("NMDS stress:", nmds_result$stress, "\n")
  
  # Extract NMDS scores
  nmds_scores <- as.data.frame(scores(nmds_result))
  nmds_scores$Locality <- as.factor(metadata_matched$Locality)
  nmds_scores$habitat_type <- as.factor(metadata_matched$habitat_type)
  
  # Create color palette for localities
  n_localities <- length(unique(nmds_scores$Locality))
  colors <- rainbow(n_localities)
  
  # Create shape palette for habitat types
  n_habitats <- length(unique(nmds_scores$habitat_type))
  shapes <- c(16, 17, 18, 15, 3, 4, 8, 11)[1:n_habitats]  # Different point shapes
  
  # Create NMDS plot
  p <- ggplot(nmds_scores, aes(x = NMDS1, y = NMDS2)) +
    geom_point(aes(color = Locality, shape = habitat_type), size = 3, alpha = 0.7) +
    scale_color_manual(values = colors) +
    scale_shape_manual(values = shapes) +
    theme_bw() +
    theme(panel.grid = element_blank()) +
    labs(title = paste("NMDS Ordination -", dataset_name),
         subtitle = paste("Stress =", round(nmds_result$stress, 3))) +
    guides(color = guide_legend(title = "Locality"),
           shape = guide_legend(title = "Habitat Type"))
  
  # PERMANOVA tests
  cat("\n--- PERMANOVA Results ---\n")
  
  # PERMANOVA for Locality
  permanova_locality <- adonis2(dist_matrix ~ Locality, data = metadata_matched, permutations = 999)
  cat("PERMANOVA - Locality:\n")
  print(permanova_locality)
  
  # PERMANOVA for habitat_type
  permanova_habitat <- adonis2(dist_matrix ~ habitat_type, data = metadata_matched, permutations = 999)
  cat("\nPERMANOVA - Habitat Type:\n")
  print(permanova_habitat)
  
  # Combined PERMANOVA
  permanova_combined <- adonis2(dist_matrix ~ Locality + habitat_type, data = metadata_matched, permutations = 999)
  cat("\nPERMANOVA - Combined (Locality + Habitat Type):\n")
  print(permanova_combined)
  
  # Betadispersion analysis
  cat("\n--- Betadispersion Analysis ---\n")
  
  # Betadispersion for Locality
  beta_locality <- betadisper(dist_matrix, metadata_matched$Locality)
  beta_locality_test <- permutest(beta_locality, pairwise = TRUE, permutations = 999)
  cat("Betadispersion - Locality:\n")
  print(beta_locality_test)
  
  # Betadispersion for habitat_type
  beta_habitat <- betadisper(dist_matrix, metadata_matched$habitat_type)
  beta_habitat_test <- permutest(beta_habitat, pairwise = TRUE, permutations = 999)
  cat("\nBetadispersion - Habitat Type:\n")
  print(beta_habitat_test)
  
  return(list(plot = p, nmds = nmds_result, 
              permanova_locality = permanova_locality,
              permanova_habitat = permanova_habitat,
              permanova_combined = permanova_combined,
              beta_locality = beta_locality_test,
              beta_habitat = beta_habitat_test))
}

# Main analysis
cat("Starting NMDS analysis for three datasets...\n")

# Load datasets
datasets <- list(
  bacteria = load_dataset("OTU_table_bacteria.xlsx", "metadata_bacteria.xlsx", "taxonomy_bacteria.xlsx", "Bacteria"),
  fungi = load_dataset("OTU_table_fungi.xlsx", "metadata_fungi.xlsx", "taxonomy_fungi.xlsx", "Fungi"),
  metazoa = load_dataset("OTU_table_metazoa.xlsx", "metadata_metazoa.xlsx", "taxonomy_metazoa.xlsx", "Metazoa")
)

# Perform analyses
results <- list()
plots <- list()

for (dataset_name in names(datasets)) {
  result <- perform_nmds_analysis(datasets[[dataset_name]], dataset_name)
  results[[dataset_name]] <- result
  plots[[dataset_name]] <- result$plot
}

# Create combined plot
combined_plot <- grid.arrange(plots$bacteria, plots$fungi, plots$metazoa, ncol = 2)

# Save plots
ggsave("nmds_bacteria.png", plots$bacteria, width = 10, height = 8, dpi = 300)
ggsave("nmds_fungi.png", plots$fungi, width = 10, height = 8, dpi = 300)
ggsave("nmds_metazoa.png", plots$metazoa, width = 10, height = 8, dpi = 300)
ggsave("nmds_combined.png", combined_plot, width = 15, height = 12, dpi = 300)

cat("\nAnalysis completed! Plots saved as PNG files.\n")
cat("Check the console output above for statistical results.\n")
