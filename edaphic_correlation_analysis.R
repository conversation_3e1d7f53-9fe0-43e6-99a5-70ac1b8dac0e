# Edaphic Parameters vs Community Composition Analysis
# Author: Generated for microbiome analysis
# Date: 2025-08-22

# Load required libraries
library(vegan)
library(readxl)
library(ggplot2)
library(dplyr)
library(gridExtra)
library(corrplot)
library(RColorBrewer)

# Set working directory
setwd("c:/Users/<USER>/Desktop/beta")

# Function to load and prepare data for each dataset
load_dataset <- function(otu_file, metadata_file, dataset_name) {
  cat("Loading", dataset_name, "dataset...\n")
  
  # Load data
  otu_table <- read_excel(otu_file)
  metadata <- read_excel(metadata_file)
  
  # Display structure for debugging
  cat("OTU table dimensions:", dim(otu_table), "\n")
  cat("Metadata dimensions:", dim(metadata), "\n")
  
  return(list(otu = otu_table, metadata = metadata))
}

# Function to prepare OTU matrix and apply Hellinger transformation
prepare_otu_matrix <- function(otu_table) {
  # Remove the first column (OTU IDs) and transpose so samples are rows
  otu_matrix <- as.matrix(otu_table[,-1])  # Remove 'otu' column
  otu_matrix <- t(otu_matrix)  # Transpose: samples as rows, OTUs as columns
  
  # Set OTU IDs as column names
  colnames(otu_matrix) <- otu_table$otu
  
  # Remove OTUs (columns) with all zeros
  otu_matrix <- otu_matrix[, colSums(otu_matrix) > 0]
  
  # Remove samples (rows) with all zeros
  otu_matrix <- otu_matrix[rowSums(otu_matrix) > 0, ]
  
  return(otu_matrix)
}

# Function to prepare environmental data
prepare_environmental_data <- function(metadata, otu_matrix) {
  # Define edaphic parameters (excluding categorical variables)
  edaphic_params <- c("Skeleton", "Clay", "Fine silt", "Coarse silt", "Sand", 
                      "pH", "Conductivity", "N", "OC", "Pex")
  
  # Match metadata with OTU samples
  metadata_matched <- metadata[metadata$Sample %in% rownames(otu_matrix), ]
  otu_matrix_matched <- otu_matrix[rownames(otu_matrix) %in% metadata_matched$Sample, ]
  
  # Reorder to match
  metadata_matched <- metadata_matched[match(rownames(otu_matrix_matched), metadata_matched$Sample), ]
  
  # Extract environmental data
  env_data <- metadata_matched[, edaphic_params, drop = FALSE]
  
  # Convert to numeric and handle missing values
  env_data <- as.data.frame(lapply(env_data, as.numeric))
  rownames(env_data) <- metadata_matched$Sample
  
  # Remove variables with all NA or constant values
  env_data <- env_data[, sapply(env_data, function(x) !all(is.na(x)) && var(x, na.rm = TRUE) > 0)]
  
  # Remove samples with any missing environmental data
  complete_samples <- complete.cases(env_data)
  env_data <- env_data[complete_samples, , drop = FALSE]
  otu_matrix_matched <- otu_matrix_matched[complete_samples, , drop = FALSE]
  
  cat("Environmental variables included:", colnames(env_data), "\n")
  cat("Samples with complete environmental data:", nrow(env_data), "\n")
  
  return(list(env = env_data, otu = otu_matrix_matched, metadata = metadata_matched[complete_samples, ]))
}

# Function to perform comprehensive edaphic correlation analysis
perform_edaphic_analysis <- function(dataset_list, dataset_name) {
  cat("\n=== Edaphic Correlation Analysis for", dataset_name, "===\n")
  
  # Prepare OTU matrix
  otu_matrix <- prepare_otu_matrix(dataset_list$otu)
  
  # Prepare environmental data
  env_otu_data <- prepare_environmental_data(dataset_list$metadata, otu_matrix)
  env_data <- env_otu_data$env
  otu_matrix_matched <- env_otu_data$otu
  
  if (nrow(env_data) < 10) {
    cat("Warning: Too few samples with complete environmental data for", dataset_name, "\n")
    return(NULL)
  }
  
  # Apply Hellinger transformation
  otu_hellinger <- decostand(otu_matrix_matched, method = "hellinger")
  
  # Calculate distance matrices
  community_dist <- vegdist(otu_hellinger, method = "bray")
  env_dist <- dist(scale(env_data))  # Standardize environmental variables
  
  # 1. MANTEL TEST
  cat("\n--- Mantel Test ---\n")
  mantel_result <- mantel(community_dist, env_dist, method = "pearson", permutations = 999)
  print(mantel_result)
  
  # 2. PARTIAL MANTEL TESTS (for each environmental variable)
  cat("\n--- Partial Mantel Tests (Individual Variables) ---\n")
  partial_mantel_results <- list()
  for (var in colnames(env_data)) {
    var_dist <- dist(scale(env_data[, var, drop = FALSE]))
    partial_mantel <- mantel(community_dist, var_dist, method = "pearson", permutations = 999)
    partial_mantel_results[[var]] <- partial_mantel
    cat(paste(var, ": r =", round(partial_mantel$statistic, 3), 
              ", p =", partial_mantel$signif, "\n"))
  }
  
  # 3. REDUNDANCY ANALYSIS (RDA)
  cat("\n--- Redundancy Analysis (RDA) ---\n")
  rda_result <- rda(otu_hellinger ~ ., data = env_data)
  print(summary(rda_result))
  
  # Test significance of RDA
  rda_anova <- anova(rda_result, permutations = 999)
  cat("\nRDA significance test:\n")
  print(rda_anova)
  
  # Test significance of individual axes
  rda_axes <- anova(rda_result, by = "axis", permutations = 999)
  cat("\nRDA axes significance:\n")
  print(rda_axes)
  
  # Test significance of individual terms
  rda_terms <- anova(rda_result, by = "terms", permutations = 999)
  cat("\nRDA terms significance:\n")
  print(rda_terms)
  
  # 4. ENVIRONMENTAL FITTING (envfit)
  cat("\n--- Environmental Vector Fitting ---\n")
  # First perform unconstrained ordination (PCA on Hellinger-transformed data)
  pca_result <- rda(otu_hellinger)
  envfit_result <- envfit(pca_result, env_data, permutations = 999)
  print(envfit_result)
  
  # 5. CREATE PLOTS
  
  # RDA biplot
  rda_scores <- scores(rda_result)
  site_scores <- as.data.frame(rda_scores$sites)
  env_scores <- as.data.frame(rda_scores$biplot)
  
  # Add locality and habitat information
  site_scores$Locality <- env_otu_data$metadata$Locality
  site_scores$habitat_type <- env_otu_data$metadata$habitat_type
  
  # Create color palette
  n_localities <- length(unique(site_scores$Locality))
  colors <- rainbow(n_localities)
  
  # Create shape palette
  n_habitats <- length(unique(site_scores$habitat_type))
  shapes <- c(16, 17, 18, 15, 3, 4, 8, 11)[1:n_habitats]
  
  # RDA plot
  rda_plot <- ggplot(site_scores, aes(x = RDA1, y = RDA2)) +
    geom_point(aes(color = Locality, shape = habitat_type), size = 3, alpha = 0.7) +
    scale_color_manual(values = colors) +
    scale_shape_manual(values = shapes) +
    theme_bw() +
    theme(panel.grid = element_blank()) +
    labs(title = paste("RDA -", dataset_name, "(Hellinger-transformed)"),
         subtitle = paste("Constrained by edaphic parameters"),
         x = paste("RDA1 (", round(summary(rda_result)$cont$importance[2,1]*100, 1), "%)", sep=""),
         y = paste("RDA2 (", round(summary(rda_result)$cont$importance[2,2]*100, 1), "%)", sep="")) +
    guides(color = guide_legend(title = "Locality"),
           shape = guide_legend(title = "Habitat Type"))
  
  # Add environmental vectors
  if (nrow(env_scores) > 0) {
    # Scale arrows for better visualization
    arrow_scale <- 3
    rda_plot <- rda_plot +
      geom_segment(data = env_scores, 
                   aes(x = 0, y = 0, xend = RDA1 * arrow_scale, yend = RDA2 * arrow_scale),
                   arrow = arrow(length = unit(0.3, "cm")), 
                   color = "red", size = 0.8) +
      geom_text(data = env_scores,
                aes(x = RDA1 * arrow_scale * 1.1, y = RDA2 * arrow_scale * 1.1, 
                    label = rownames(env_scores)),
                color = "red", size = 3, fontface = "bold")
  }
  
  # Environmental correlation heatmap
  env_cor <- cor(env_data, use = "complete.obs")
  
  return(list(
    mantel = mantel_result,
    partial_mantel = partial_mantel_results,
    rda = rda_result,
    rda_anova = rda_anova,
    rda_axes = rda_axes,
    rda_terms = rda_terms,
    envfit = envfit_result,
    rda_plot = rda_plot,
    env_correlation = env_cor,
    env_data = env_data
  ))
}

# Main analysis
cat("Starting edaphic correlation analysis for three datasets...\n")

# Load datasets
datasets <- list(
  bacteria = load_dataset("OTU_table_bacteria.xlsx", "metadata_bacteria.xlsx", "Bacteria"),
  fungi = load_dataset("OTU_table_fungi.xlsx", "metadata_fungi.xlsx", "Fungi"),
  metazoa = load_dataset("OTU_table_metazoa.xlsx", "metadata_metazoa.xlsx", "Metazoa")
)

# Perform analyses
results <- list()
plots <- list()

for (dataset_name in names(datasets)) {
  result <- perform_edaphic_analysis(datasets[[dataset_name]], dataset_name)
  if (!is.null(result)) {
    results[[dataset_name]] <- result
    plots[[dataset_name]] <- result$rda_plot
  }
}

# Save plots
if (length(plots) > 0) {
  for (dataset_name in names(plots)) {
    ggsave(paste0("rda_", tolower(dataset_name), ".png"), plots[[dataset_name]], 
           width = 12, height = 10, dpi = 300)
  }
  
  # Create combined plot if we have multiple datasets
  if (length(plots) > 1) {
    combined_plot <- do.call(grid.arrange, c(plots, ncol = 2))
    ggsave("rda_combined.png", combined_plot, width = 18, height = 12, dpi = 300)
  }
}

cat("\nEdaphic correlation analysis completed!\n")
cat("RDA plots saved as PNG files.\n")
cat("Check the console output above for detailed statistical results.\n")
