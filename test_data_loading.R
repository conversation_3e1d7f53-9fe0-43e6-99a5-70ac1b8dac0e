# Test script to verify data loading and processing
library(readxl)
library(vegan)

# Load bacteria data
cat("Loading bacteria dataset...\n")
otu_bacteria <- read_excel("OTU_table_bacteria.xlsx")
metadata_bacteria <- read_excel("metadata_bacteria.xlsx")

cat("Original OTU table dimensions:", dim(otu_bacteria), "\n")
cat("Metadata dimensions:", dim(metadata_bacteria), "\n")

# Prepare OTU matrix
otu_matrix <- as.matrix(otu_bacteria[,-1])  # Remove 'otu' column
otu_matrix <- t(otu_matrix)  # Transpose: samples as rows, OTUs as columns
colnames(otu_matrix) <- otu_bacteria$otu

cat("Transposed OTU matrix dimensions:", dim(otu_matrix), "\n")
cat("Sample names (first 10):", rownames(otu_matrix)[1:10], "\n")

# Remove OTUs with all zeros
otu_matrix <- otu_matrix[, colSums(otu_matrix) > 0]
cat("After removing zero OTUs:", dim(otu_matrix), "\n")

# Remove samples with all zeros
otu_matrix <- otu_matrix[rowSums(otu_matrix) > 0, ]
cat("After removing zero samples:", dim(otu_matrix), "\n")

# Match with metadata
metadata_matched <- metadata_bacteria[metadata_bacteria$Sample %in% rownames(otu_matrix), ]
otu_matrix_matched <- otu_matrix[rownames(otu_matrix) %in% metadata_matched$Sample, ]

cat("Matched metadata rows:", nrow(metadata_matched), "\n")
cat("Matched OTU matrix rows:", nrow(otu_matrix_matched), "\n")

# Reorder to match
metadata_matched <- metadata_matched[match(rownames(otu_matrix_matched), metadata_matched$Sample), ]

cat("Final check - sample names match:", all(rownames(otu_matrix_matched) == metadata_matched$Sample), "\n")

# Check for unique localities and habitat types
cat("Unique localities:", unique(metadata_matched$Locality), "\n")
cat("Unique habitat types:", unique(metadata_matched$habitat_type), "\n")

# Test distance calculation
dist_matrix <- vegdist(otu_matrix_matched, method = "bray")
cat("Distance matrix calculated successfully. Size:", length(dist_matrix), "\n")

# Test NMDS
set.seed(123)
nmds_result <- metaMDS(dist_matrix, k = 2, trymax = 20)
cat("NMDS stress:", nmds_result$stress, "\n")

cat("Data loading and processing test completed successfully!\n")
