# Simplified Edaphic Parameters vs Community Composition Analysis
# Author: Generated for microbiome analysis
# Date: 2025-08-22

# Load required libraries
library(vegan)
library(readxl)
library(ggplot2)
library(dplyr)

# Set working directory
setwd("c:/Users/<USER>/Desktop/beta")

# Function to load and prepare data for each dataset
load_dataset <- function(otu_file, metadata_file, dataset_name) {
  cat("Loading", dataset_name, "dataset...\n")
  
  # Load data
  otu_table <- read_excel(otu_file)
  metadata <- read_excel(metadata_file)
  
  return(list(otu = otu_table, metadata = metadata))
}

# Function to prepare OTU matrix and apply Hellinger transformation
prepare_otu_matrix <- function(otu_table) {
  # Remove the first column (OTU IDs) and transpose so samples are rows
  otu_matrix <- as.matrix(otu_table[,-1])  # Remove 'otu' column
  otu_matrix <- t(otu_matrix)  # Transpose: samples as rows, OTUs as columns
  
  # Set OTU IDs as column names
  colnames(otu_matrix) <- otu_table$otu
  
  # Remove OTUs (columns) with all zeros
  otu_matrix <- otu_matrix[, colSums(otu_matrix) > 0]
  
  # Remove samples (rows) with all zeros
  otu_matrix <- otu_matrix[rowSums(otu_matrix) > 0, ]
  
  return(otu_matrix)
}

# Function to prepare environmental data
prepare_environmental_data <- function(metadata, otu_matrix) {
  # Define edaphic parameters (excluding categorical variables)
  edaphic_params <- c("Skeleton", "Clay", "Fine silt", "Coarse silt", "Sand", 
                      "pH", "Conductivity", "N", "OC", "Pex")
  
  # Match metadata with OTU samples
  metadata_matched <- metadata[metadata$Sample %in% rownames(otu_matrix), ]
  otu_matrix_matched <- otu_matrix[rownames(otu_matrix) %in% metadata_matched$Sample, ]
  
  # Reorder to match
  metadata_matched <- metadata_matched[match(rownames(otu_matrix_matched), metadata_matched$Sample), ]
  
  # Extract environmental data
  env_data <- metadata_matched[, edaphic_params, drop = FALSE]
  
  # Convert to numeric and handle missing values
  env_data <- as.data.frame(lapply(env_data, as.numeric))
  rownames(env_data) <- metadata_matched$Sample
  
  # Remove variables with all NA or constant values
  env_data <- env_data[, sapply(env_data, function(x) !all(is.na(x)) && var(x, na.rm = TRUE) > 0)]
  
  # Remove samples with any missing environmental data
  complete_samples <- complete.cases(env_data)
  env_data <- env_data[complete_samples, , drop = FALSE]
  otu_matrix_matched <- otu_matrix_matched[complete_samples, , drop = FALSE]
  
  cat("Environmental variables included:", colnames(env_data), "\n")
  cat("Samples with complete environmental data:", nrow(env_data), "\n")
  
  return(list(env = env_data, otu = otu_matrix_matched, metadata = metadata_matched[complete_samples, ]))
}

# Function to perform simplified edaphic correlation analysis
perform_edaphic_analysis <- function(dataset_list, dataset_name) {
  cat("\n=== Edaphic Correlation Analysis for", dataset_name, "===\n")
  
  # Prepare OTU matrix
  otu_matrix <- prepare_otu_matrix(dataset_list$otu)
  
  # Prepare environmental data
  env_otu_data <- prepare_environmental_data(dataset_list$metadata, otu_matrix)
  env_data <- env_otu_data$env
  otu_matrix_matched <- env_otu_data$otu
  
  if (nrow(env_data) < 10) {
    cat("Warning: Too few samples with complete environmental data for", dataset_name, "\n")
    return(NULL)
  }
  
  # Apply Hellinger transformation
  otu_hellinger <- decostand(otu_matrix_matched, method = "hellinger")
  
  # Calculate distance matrices
  community_dist <- vegdist(otu_hellinger, method = "bray")
  env_dist <- dist(scale(env_data))  # Standardize environmental variables
  
  # 1. MANTEL TEST
  cat("\n--- Mantel Test ---\n")
  mantel_result <- mantel(community_dist, env_dist, method = "pearson", permutations = 999)
  print(mantel_result)
  
  # 2. PARTIAL MANTEL TESTS (for each environmental variable)
  cat("\n--- Partial Mantel Tests (Individual Variables) ---\n")
  partial_mantel_results <- list()
  for (var in colnames(env_data)) {
    var_dist <- dist(scale(env_data[, var, drop = FALSE]))
    partial_mantel <- mantel(community_dist, var_dist, method = "pearson", permutations = 999)
    partial_mantel_results[[var]] <- partial_mantel
    cat(paste(var, ": r =", round(partial_mantel$statistic, 3), 
              ", p =", partial_mantel$signif, "\n"))
  }
  
  # 3. REDUNDANCY ANALYSIS (RDA) - simplified
  cat("\n--- Redundancy Analysis (RDA) ---\n")
  rda_result <- rda(otu_hellinger ~ ., data = env_data)
  
  # Print variance partitioning
  cat("Variance partitioning:\n")
  cat("Total variance:", round(rda_result$tot.chi, 4), "\n")
  cat("Constrained variance:", round(rda_result$CCA$tot.chi, 4), 
      "(", round(rda_result$CCA$tot.chi/rda_result$tot.chi*100, 1), "%)\n")
  cat("Unconstrained variance:", round(rda_result$CA$tot.chi, 4), 
      "(", round(rda_result$CA$tot.chi/rda_result$tot.chi*100, 1), "%)\n")
  
  # Test significance of RDA
  rda_anova <- anova(rda_result, permutations = 999)
  cat("\nRDA significance test:\n")
  print(rda_anova)
  
  # Test significance of individual terms (reduced permutations for speed)
  rda_terms <- anova(rda_result, by = "terms", permutations = 199)
  cat("\nRDA terms significance:\n")
  print(rda_terms)
  
  # 4. ENVIRONMENTAL FITTING (envfit) - simplified
  cat("\n--- Environmental Vector Fitting ---\n")
  # First perform unconstrained ordination (PCA on Hellinger-transformed data)
  pca_result <- rda(otu_hellinger)
  envfit_result <- envfit(pca_result, env_data, permutations = 199)
  print(envfit_result)
  
  # 5. Summary of most important correlations
  cat("\n--- Summary of Key Results ---\n")
  cat("Overall Mantel correlation (community vs environment): r =", 
      round(mantel_result$statistic, 3), ", p =", mantel_result$signif, "\n")
  
  # Find strongest individual correlations
  mantel_r_values <- sapply(partial_mantel_results, function(x) x$statistic)
  mantel_p_values <- sapply(partial_mantel_results, function(x) x$signif)
  
  # Sort by correlation strength
  sorted_vars <- order(abs(mantel_r_values), decreasing = TRUE)
  
  cat("Strongest individual variable correlations:\n")
  for (i in 1:min(5, length(sorted_vars))) {
    var_idx <- sorted_vars[i]
    var_name <- names(mantel_r_values)[var_idx]
    cat(paste("  ", var_name, ": r =", round(mantel_r_values[var_idx], 3), 
              ", p =", mantel_p_values[var_idx], "\n"))
  }
  
  cat("RDA explained variance:", round(rda_result$CCA$tot.chi/rda_result$tot.chi*100, 1), "%\n")
  cat("RDA significance: p =", rda_anova$`Pr(>F)`[1], "\n")
  
  return(list(
    mantel = mantel_result,
    partial_mantel = partial_mantel_results,
    rda = rda_result,
    rda_anova = rda_anova,
    rda_terms = rda_terms,
    envfit = envfit_result,
    env_data = env_data
  ))
}

# Main analysis
cat("Starting simplified edaphic correlation analysis for three datasets...\n")

# Load datasets
datasets <- list(
  bacteria = load_dataset("OTU_table_bacteria.xlsx", "metadata_bacteria.xlsx", "Bacteria"),
  fungi = load_dataset("OTU_table_fungi.xlsx", "metadata_fungi.xlsx", "Fungi"),
  metazoa = load_dataset("OTU_table_metazoa.xlsx", "metadata_metazoa.xlsx", "Metazoa")
)

# Perform analyses
results <- list()

for (dataset_name in names(datasets)) {
  result <- perform_edaphic_analysis(datasets[[dataset_name]], dataset_name)
  if (!is.null(result)) {
    results[[dataset_name]] <- result
  }
}

cat("\n=== OVERALL SUMMARY ===\n")
cat("Edaphic correlation analysis completed for all datasets!\n")
cat("Key findings:\n")

for (dataset_name in names(results)) {
  if (!is.null(results[[dataset_name]])) {
    mantel_r <- round(results[[dataset_name]]$mantel$statistic, 3)
    mantel_p <- results[[dataset_name]]$mantel$signif
    rda_var <- round(results[[dataset_name]]$rda$CCA$tot.chi/results[[dataset_name]]$rda$tot.chi*100, 1)
    rda_p <- results[[dataset_name]]$rda_anova$`Pr(>F)`[1]
    
    cat(paste("\n", dataset_name, ":\n"))
    cat(paste("  Overall correlation: r =", mantel_r, ", p =", mantel_p, "\n"))
    cat(paste("  RDA explained variance:", rda_var, "%, p =", rda_p, "\n"))
  }
}

cat("\nCheck the console output above for detailed statistical results.\n")
